<template>
  <!-- H5环境使用img标签 -->
  <!-- #ifdef H5 -->
  <img ref="imageRef" :src="fullImageUrl" v-bind="$attrs" :alt="alt" />
  <!-- #endif -->

  <!-- 非H5环境使用image标签 -->
  <!-- #ifndef H5 -->
  <image ref="imageRef" :src="processedImageUrl || fullImageUrl" v-bind="$attrs" :alt="alt" mode="aspectFill" />
  <!-- #endif -->
</template>

<script>
export default {
  name: 'ImageRestore',
  props: {
    imageUrl: {
      type: String,
      required: true,
      default: ''
    },
    alt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      processedImageUrl: '' // 用于存储处理后的图片URL
    }
  },
  computed: {
    fullImageUrl() {
      if (!this.imageUrl) return '';

      // 如果已经是完整URL，直接返回
      if (this.imageUrl.startsWith('http://') || this.imageUrl.startsWith('https://')) {
        return this.imageUrl;
      }

      // 如果是相对路径，拼接当前域名
      // #ifdef H5
      const baseUrl = window.location.origin;
      // #endif
      // #ifndef H5
      const baseUrl = 'https://api.player.4ii.cc';
      // #endif
      return baseUrl + this.imageUrl;
    }
  },
  mounted() {
    // #ifdef H5
    this.fetchImageAndRemoveFirstFourBytes(this.fullImageUrl, this.$refs.imageRef);
    // #endif

    // #ifndef H5
    this.processedImageUrl = this.fullImageUrl;
    this.fetchImageAndRemoveFirstFourBytes(this.fullImageUrl);
    // #endif
  },
  watch: {
    imageUrl(newUrl) {
      // #ifdef H5
      this.fetchImageAndRemoveFirstFourBytes(this.fullImageUrl, this.$refs.imageRef);
      // #endif

      // #ifndef H5
      this.processedImageUrl = this.fullImageUrl;
      this.fetchImageAndRemoveFirstFourBytes(this.fullImageUrl);
      // #endif
    }
  },
  methods: {
    async fetchImageAndRemoveFirstFourBytes(imageUrl, imgElement) {
      if (!imageUrl) {
        return;
      }

      const self = this;

      try {
        // #ifdef H5
        const response = await fetch(imageUrl, { cache: 'default' });
        if (!response.ok) {
          throw new Error(`无法获取图片: ${response.status} ${response.statusText}`);
        }

        const buffer = await response.arrayBuffer();
        const newBuffer = buffer.slice(4);
        const contentType = response.headers.get('content-type') || 'image/jpeg';
        const blob = new Blob([newBuffer], { type: contentType });
        const objectURL = URL.createObjectURL(blob);

        if (imgElement) {
          imgElement.src = objectURL;
          imgElement.onload = () => {
            URL.revokeObjectURL(objectURL);
          };
        }
        // #endif

        // #ifndef H5
        uni.request({
          url: imageUrl,
          method: 'GET',
          responseType: 'arraybuffer',
          success: function(res) {
            try {
              const buffer = res.data;
              const newBuffer = buffer.slice(4);

              // 将ArrayBuffer转换为base64
              const uint8Array = new Uint8Array(newBuffer);
              let binary = '';
              for (let i = 0; i < uint8Array.length; i++) {
                binary += String.fromCharCode(uint8Array[i]);
              }
              const base64 = btoa(binary);
              self.processedImageUrl = 'data:image/jpeg;base64,' + base64;
            } catch (error) {
              self.processedImageUrl = self.fullImageUrl;
            }
          },
          fail: function(error) {
            self.processedImageUrl = self.fullImageUrl;
          }
        });
        // #endif
      } catch (error) {
        // #ifdef H5
        if (imgElement) {
          imgElement.src = this.fullImageUrl;
        }
        // #endif

        // #ifndef H5
        this.processedImageUrl = this.fullImageUrl;
        // #endif
      }
    }
  }
}
</script>

<style scoped>

</style>
